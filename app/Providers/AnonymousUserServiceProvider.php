<?php

namespace App\Providers;

use App\Services\AnonymousUserService;
use Illuminate\Support\ServiceProvider;

class AnonymousUserServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(AnonymousUserService::class, function ($app) {
            return new AnonymousUserService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
