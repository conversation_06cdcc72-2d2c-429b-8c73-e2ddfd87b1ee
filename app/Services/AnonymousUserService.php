<?php

namespace App\Services;

use App\Models\AnonymousUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class AnonymousUserService
{
    const SESSION_KEY = 'anonymous_user_id';
    const SESSION_NAME_KEY = 'anonymous_user_name';

    /**
     * Get or create an anonymous user identity for the current session
     */
    public function getOrCreateAnonymousUser(Request $request): AnonymousUser
    {
        // If user is already authenticated with a real user, don't create anonymous user
        if (Auth::check()) {
            $user = Auth::user();
            if (!($user instanceof AnonymousUser)) {
                throw new \Exception('Real user is already authenticated');
            }
        }

        $sessionId = $request->session()->getId();

        // Try to get existing anonymous user from session
        $anonymousId = Session::get(self::SESSION_KEY);
        $anonymousName = Session::get(self::SESSION_NAME_KEY);

        // If no anonymous user exists, create one
        if (!$anonymousId) {
            $anonymousId = $this->generateAnonymousId($sessionId);
            $anonymousName = $this->generateAnonymousName();

            Session::put(self::SESSION_KEY, $anonymousId);
            Session::put(self::SESSION_NAME_KEY, $anonymousName);
        }

        // Create anonymous user data array
        $anonymousData = [
            'userId' => $anonymousId,
            'name' => $anonymousName,
            'anon' => true,
            'sessionId' => $sessionId,
        ];

        // Create the anonymous user but don't authenticate it with Auth::login()
        // Instead, we'll handle authentication in the broadcasting controller
        $anonymousUser = new AnonymousUser($anonymousData);

        return $anonymousUser;
    }

    /**
     * Generate a unique anonymous user ID
     */
    private function generateAnonymousId(string $sessionId): string
    {
        // Create a consistent but unique ID based on session
        return hexdec(substr(md5($sessionId . config('app.key')), 0, 8)) * -1;
    }

    /**
     * Generate a random anonymous user name
     */
    private function generateAnonymousName(): string
    {
        $adjectives = [
            'Espectador', 'Visitante', 'Observador', 'Curioso', 'Invitado',
            'Anónimo', 'Misterioso', 'Silencioso', 'Oculto', 'Secreto'
        ];

        $nouns = [
            'Azul', 'Rojo', 'Verde', 'Dorado', 'Plateado',
            'Rápido', 'Valiente', 'Astuto', 'Sabio', 'Fuerte'
        ];

        $adjective = $adjectives[array_rand($adjectives)];
        $noun = $nouns[array_rand($nouns)];
        $number = rand(100, 999);

        return "{$adjective} {$noun} {$number}";
    }

    /**
     * Check if a user ID is anonymous
     */
    public function isAnonymousUserId(string $userId): bool
    {
        return str_starts_with($userId, 'anon_');
    }

    /**
     * Get anonymous user data from session
     */
    public function getAnonymousUserFromSession(): ?AnonymousUser
    {
        $anonymousId = Session::get(self::SESSION_KEY);
        $anonymousName = Session::get(self::SESSION_NAME_KEY);

        if (!$anonymousId || !$anonymousName) {
            return null;
        }

        $anonymousData = [
            'userId' => $anonymousId,
            'name' => $anonymousName,
            'anon' => true,
        ];

        return new AnonymousUser($anonymousData);
    }

    /**
     * Get anonymous user data as array (for backward compatibility)
     */
    public function getAnonymousUserDataFromSession(): ?array
    {
        $anonymousId = Session::get(self::SESSION_KEY);
        $anonymousName = Session::get(self::SESSION_NAME_KEY);

        if (!$anonymousId || !$anonymousName) {
            return null;
        }

        return [
            'userId' => $anonymousId,
            'name' => $anonymousName,
            'anon' => true,
        ];
    }

    /**
     * Clear anonymous user data from session
     */
    public function clearAnonymousUser(): void
    {
        Session::forget([self::SESSION_KEY, self::SESSION_NAME_KEY]);
    }

    /**
     * Update anonymous user name
     */
    public function updateAnonymousUserName(string $newName): void
    {
        Session::put(self::SESSION_NAME_KEY, $newName);
    }
}
