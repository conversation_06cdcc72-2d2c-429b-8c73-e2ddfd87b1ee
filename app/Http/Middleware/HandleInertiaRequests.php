<?php

namespace App\Http\Middleware;

use App\Services\AnonymousUserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $userId = $request->user()?->id;
        $userName = $request->user()?->name;
        $avatarHash = $request->user()?->getAvatarHash() ?? null;
        $guest = false;
        $walletBalance = 0;
        $anonymousUser = null;

        if (Auth::guest()) {
            // Use the new anonymous user service
            $anonymousUserService = app(AnonymousUserService::class);
            $anonymousUserModel = $anonymousUserService->getOrCreateAnonymousUser($request);

            // Convert to array for frontend compatibility
            $anonymousUser = [
                'userId' => $anonymousUserModel->id,
                'name' => $anonymousUserModel->name,
                'anon' => $anonymousUserModel->anonymous,
            ];

            // Keep backward compatibility with existing guest logic
            $userId = $anonymousUser['userId'];
            $userName = $anonymousUser['name'];
            $guest = true;
        } else {
            // Get wallet balance for authenticated users
            $walletService = app(\App\Services\WalletService::class);
            $walletBalance = $walletService->getUserBalance($request->user());
        }

        // Obtener traducciones para el frontend
        $translations = [];
        $translationKeys = [
            'Sign in to continue',
            'To purchase the package',
            'and get',
            'credits, you need to sign in or create an account.',
            'Sign in',
            'Create account',
            'Cancel'
        ];

        foreach ($translationKeys as $key) {
            $translations[$key] = __($key);
        }

        // Get flash messages from session
        $flash = [
            'success' => $request->session()->get('success'),
            'error' => $request->session()->get('error'),
            'info' => $request->session()->get('info'),
        ];

        return [
            ...parent::share($request),
            'userId' => $userId,
            'userName' => $userName,
            'guest' => $guest,
            'walletBalance' => $walletBalance,
            'avatarHash' => $avatarHash,
            'anonymousUser' => $anonymousUser,
            'translations' => $translations,
            'flash' => $flash,
            'ziggy' => fn() => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
        ];
    }
}
