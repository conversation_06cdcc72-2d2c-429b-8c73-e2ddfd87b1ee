<?php

namespace App\Http\Controllers;

use App\Services\AnonymousUserService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class AuthenticateBroadcastGuests
{
    protected AnonymousUserService $anonymousUserService;

    public function __construct(AnonymousUserService $anonymousUserService)
    {
        $this->anonymousUserService = $anonymousUserService;
    }
    public function handle(Request $request, \Closure $next): Response
    {
        if (Auth::guard('web')->check()) {
            return $next($request);
        }

        Auth::login($this->anonymousUserService->getOrCreateAnonymousUser($request));

        return $next($request);
    }
}
