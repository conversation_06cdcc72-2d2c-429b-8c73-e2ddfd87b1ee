<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;

class Anonymous<PERSON>ser implements Authenticatable
{
    public $id;
    public $name;
    public $anonymous = true;
    public $anonymousData;

    public function __construct(array $anonymousData)
    {
        $this->id = $anonymousData['userId'];
        $this->name = $anonymousData['name'];
        $this->anonymousData = $anonymousData;
    }

    public function getAuthIdentifierName()
    {
        return 'id';
    }

    public function getAuthIdentifier()
    {
        return $this->id;
    }

    public function getAuthPassword()
    {
        return null;
    }

    public function getRememberToken()
    {
        return null;
    }

    public function setRememberToken($value)
    {
        // Do nothing
    }

    public function getRememberTokenName()
    {
        return null;
    }

    public function getAuthPasswordName()
    {
        return 'password';
    }
}
