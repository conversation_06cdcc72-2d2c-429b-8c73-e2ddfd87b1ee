<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import {computed, onMounted, useTemplateRef, ref, onUnmounted} from "vue";
import {SpectatorWebRtc} from "@/spectator-web-rtc";
import {router, usePage} from "@inertiajs/vue3";
import {Link} from '@inertiajs/vue3'
import {Prize} from "@/Pages/types";
import {UserService} from "@/services/UserService";
import axios from 'axios';

let {channelId, machine, queueInfo, userPosition, userBalance, hasEnoughPoints, isAuthenticated} = defineProps<{
  channelId: string,
  machine: { price: Prize } | any,
  queueInfo: any,
  userPosition: any,
  userBalance: number,
  hasEnoughPoints: boolean,
  isAuthenticated: boolean
}>()
const page = usePage()
const userService = UserService.getInstance();

// Initialize user service with page props
userService.initializeUser(page.props);

const userId = computed(() => userService.getUserId());
let spectatorPeerHolder: SpectatorWebRtc;
const remoteVideo$ = useTemplateRef<HTMLVideoElement>('remotevideo');
const connectionStatus = ref('Disconnected');
const hasVideoStream = ref(false);

async function init() {
  console.log('Initializing WebRTC in MachineSpectator');
  console.log('User ID:', userId.value);
  console.log('User Data:', userService.getUser());
  console.log('Is Anonymous:', userService.isAnonymous());
  console.log('Channel ID:', channelId);
  console.log('Video element exists:', !!remoteVideo$.value);

  // Make sure video element has proper attributes
  if (remoteVideo$.value) {
    remoteVideo$.value.autoplay = true;
    remoteVideo$.value.playsInline = true;
    remoteVideo$.value.muted = true; // Mute to avoid feedback
    console.log('Video element configured');
  }

  // Initialize the WebRTC connection
  spectatorPeerHolder = new SpectatorWebRtc('spectator', userId.value, channelId, null, remoteVideo$.value);

  // Set up connection status monitoring
  const checkVideoStatus = () => {
    if (remoteVideo$.value && remoteVideo$.value.srcObject) {
      hasVideoStream.value = true;
      connectionStatus.value = 'Connected';
    } else {
      hasVideoStream.value = false;
      connectionStatus.value = 'Waiting for video...';
    }
  };

  // Check status periodically
  setInterval(checkVideoStatus, 1000);
}

// Refresh queue data
async function refreshQueueData() {
  try {
    if (userPosition) {
      const [positionResponse, infoResponse] = await Promise.all([
        axios.get(route('queue.position', channelId)),
        axios.get(route('queue.info', channelId))
      ]);

      // Update the data
      Object.assign(userPosition, positionResponse.data);
      Object.assign(queueInfo, infoResponse.data);

      // If user is now the current player, redirect to game
      if (userPosition.in_queue && userPosition.should_redirect) {
        router.visit(route('machine.play', channelId));
      }
    }
  } catch (error) {
    console.error('Failed to refresh queue data:', error);
  }
}

// Join the queue
async function joinQueue() {
  try {
    const response = await axios.post(route('queue.join', channelId));
    if (response.data.success) {
      // If the user is now the current player, redirect to the game page
      if (response.data.should_redirect) {
        router.visit(route('machine.play', channelId));
      } else {
        refreshQueueData();
      }
    } else {
      // Handle redirect cases
      if (response.data.redirect === 'login') {
        // Redirect to login page
        window.location.href = response.data.redirect_url;
      } else if (response.data.redirect === 'store') {
        // Show dialog to buy points
        if (confirm(`${response.data.message} ¿Quieres ir a la tienda para comprar puntos?`)) {
          window.location.href = response.data.redirect_url;
        }
      } else {
        alert(response.data.message || 'No se pudo unir a la cola. Por favor, inténtalo de nuevo.');
      }
    }
  } catch (error) {
    console.error('Failed to join queue:', error);

    // Handle unauthorized error (not logged in)
    if (error.response && error.response.status === 401) {
      if (confirm('Debes iniciar sesión para unirte a la cola. ¿Quieres iniciar sesión ahora?')) {
        window.location.href = route('login');
      }
    } else {
      alert('Error al unirse a la cola. Por favor, inténtalo de nuevo.');
    }
  }
}

// Leave the queue
async function leaveQueue() {
  try {
    const response = await axios.post(route('queue.leave', channelId));
    if (response.data.success) {
      refreshQueueData();
    }
  } catch (error) {
    console.error('Failed to leave queue:', error);
    alert('Failed to leave queue. Please try again.');
  }
}

// Go to the queue page
function goToQueue() {
  router.visit(route('machine.queue', channelId));
}

let refreshInterval: NodeJS.Timeout | null = null;
let controllerCheckInterval: NodeJS.Timeout | null = null;

// Function to check if the controller is connected
async function checkControllerStatus() {
  try {
    const response = await axios.get(route('machine.controller-status', channelId));
    if (response.data.connected) {
      connectionStatus.value = 'Controller connected, waiting for video...';
    } else {
      connectionStatus.value = 'Controller offline';
    }
  } catch (error) {
    console.error('Failed to check controller status:', error);
    connectionStatus.value = 'Waiting for connection...';
    // Don't show error to user, just log it
  }
}

onMounted(() => {
  init();

  // Set up auto-refresh for queue data
  refreshInterval = setInterval(refreshQueueData, 5000);

  // Check if controller is connected
  checkControllerStatus();
});

onUnmounted(() => {
  // Clean up
  if (spectatorPeerHolder) {
    spectatorPeerHolder.leaving();
  }

  // Clear intervals
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }

  if (controllerCheckInterval) {
    clearInterval(controllerCheckInterval);
  }
});

let startEventCleanup = router.on('start', async function (event) {
  console.log('nav start', event)
  startEventCleanup()
  await spectatorPeerHolder.leaving()
})

window.addEventListener('beforeunload', () => spectatorPeerHolder.leaving())
</script>

<template>
  <AppLayout title="Dashboard">
    <template #header>
      <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        {{ machine.prize.title }}
      </h2>
    </template>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <!-- Live Game View -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 mb-6">
          <div class="flex flex-wrap">
            <div class="w-full md:w-7/12 pr-0 md:pr-4 mb-4 md:mb-0">
              <video ref="remotevideo" class="spectator-video rounded-lg w-full" autoplay muted playsinline controls></video>
              <div class="text-center mt-2">
                <p :class="{'text-red-500': !hasVideoStream, 'text-green-500': hasVideoStream}">
                  Estado de conexión: {{ connectionStatus }}
                </p>
                <button @click="init" class="btn btn-sm btn-primary mt-2" v-if="!hasVideoStream">
                  Reintentar conexión
                </button>
              </div>
            </div>
            <div class="w-full md:w-5/12 flex flex-col justify-between">
              <div>
                <h1 class="font-semibold tracking-tight text-3xl mb-4">
                  {{ machine.prize.title }}
                </h1>
                <p class="mb-4" v-html="machine.prize.description"></p>
                <p class="mb-4">Costo para jugar: {{ machine.prize.price }} puntos</p>
                <p v-if="isAuthenticated" class="mb-4" :class="{'text-red-500': !hasEnoughPoints, 'text-green-500': hasEnoughPoints}">
                  Tu saldo: {{ userBalance }} puntos
                  <span v-if="!hasEnoughPoints" class="text-xs">(insuficiente)</span>
                </p>
              </div>

              <div class="flex flex-col space-y-4">
                <img v-if="machine.prize.banner" :src="'/storage/' +machine.prize.banner" :title="machine.prize.title" class="w-64 mx-auto"/>

                <div class="flex space-x-4 justify-center">
                  <Link :href="route('machine.queue', channelId)" class="btn btn-primary">
                    Ver Cola
                  </Link>

                  <button v-if="userPosition && userPosition.in_queue"
                          @click="leaveQueue"
                          class="btn btn-error">
                    Salir de la Cola
                  </button>

                  <!-- Always show join queue button for non-queued users -->
                  <template v-else>
                    <div class="flex space-x-2">
                      <button @click="joinQueue" class="btn btn-accent">
                        Unirse a la Cola
                      </button>

                      <!-- Show buy points button for authenticated users with low balance -->
                      <Link v-if="isAuthenticated && !hasEnoughPoints"
                            :href="route('store.index')"
                            class="btn btn-outline">
                        Comprar Puntos
                      </Link>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Queue Information -->
        <div v-if="queueInfo" class="bg-white dark:bg-gray-800 rounded-lg p-6">
          <h2 class="text-xl font-bold mb-4">Estado de la Cola</h2>

          <!-- Current Player -->
          <div class="mb-6">
            <h3 class="text-lg font-bold mb-2">Jugador Actual</h3>
            <div v-if="queueInfo.current_player" class="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
              <p class="font-bold">{{ queueInfo.current_player.username }}</p>
              <p>Jugando desde: {{ queueInfo.current_player.started_at }}</p>
            </div>
            <div v-else class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
              <p>Nadie está jugando actualmente</p>
            </div>
          </div>

          <!-- User's Position -->
          <div v-if="userPosition && userPosition.in_queue" class="mb-6">
            <h3 class="text-lg font-bold mb-2">Tu Posición</h3>
            <div class="bg-blue-100 dark:bg-blue-900 p-4 rounded-lg">
              <p v-if="userPosition.is_playing" class="font-bold">¡Estás jugando actualmente!</p>
              <p v-else class="font-bold">Tu posición: {{ userPosition.position }}</p>
              <p v-if="!userPosition.is_playing">Tiempo estimado de espera: {{ userPosition.estimated_wait_time }}</p>
              <p>Te uniste: {{ userPosition.joined_at }}</p>
            </div>
          </div>

          <!-- Waiting Players -->
          <div>
            <h3 class="text-lg font-bold mb-2">Jugadores en Cola: {{ queueInfo.total_in_queue }}</h3>
            <div v-if="queueInfo.waiting_players.length === 0" class="text-gray-500">
              No hay jugadores esperando en la cola
            </div>
            <div v-else class="overflow-x-auto">
              <table class="table w-full">
                <thead>
                  <tr>
                    <th>Posición</th>
                    <th>Jugador</th>
                    <th>Se unió</th>
                    <th>Tiempo est. de espera</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="player in queueInfo.waiting_players" :key="player.queue_id"
                      :class="{'bg-blue-100 dark:bg-blue-900': userPosition && userPosition.in_queue && userPosition.queue_id === player.queue_id}">
                    <td>{{ player.position }}</td>
                    <td>{{ player.username }}</td>
                    <td>{{ player.joined_at }}</td>
                    <td>{{ player.estimated_wait_time }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<style scoped>
.spectator-video {
  background-color: #0d1116;
  width: 100%;
  height: auto;

  /* Use object-fit to ensure video content fits well */
  object-fit: contain;
  object-position: center;

  /* Add subtle styling */
  border: 1px solid #374151;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  /* Smooth transitions */
  transition: all 0.3s ease;
}

.spectator-video:hover {
  border-color: #60a5fa;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Optional square mode - uncomment if you want square video for spectators too */
/*
.spectator-video.square-mode {
  aspect-ratio: 1 / 1;
  object-fit: cover;
  max-width: 500px;
  margin: 0 auto;
}
*/

/* Responsive adjustments */
@media (max-width: 768px) {
  .spectator-video {
    border-radius: 4px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .spectator-video {
    border-color: #4b5563;
  }

  .spectator-video:hover {
    border-color: #93c5fd;
  }
}
</style>
