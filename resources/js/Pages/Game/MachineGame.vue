<script setup lang="ts">
import AppLayout from '@/Layouts/AppLayout.vue';
import {computed, onMounted, onUnmounted, ref, useTemplateRef} from "vue";
// import {EchoSimplePeer} from "../../echo-simple-peer";
import {EchoWebRtc} from "../../echo-web-rtc";
import {Link, router, usePage} from "@inertiajs/vue3";
import axios from "axios";

let {channelId, machine, canPlay, message, userBalance, queueInfo, userPosition} = defineProps({
  channelId: String,
  machine: Object,
  canPlay: Boolean,
  message: String,
  userBalance: Number,
  queueInfo: Object,
  userPosition: Object
})
const page = usePage()
const userId = computed(() => page.props.userId as number);
let echoSimplePeerHolder: EchoWebRtc;
const remoteVideo$ = useTemplateRef<HTMLVideoElement>('remotevideo')
const messageModal$ = useTemplateRef<HTMLDialogElement>('messageModal')
type ESTADOS =
    'Inicia'
    | 'Manten presionado para avanzar(1)'
    | 'Suelta para Detenerte(1)'
    | 'Manten presionado para avanzar(2)'
    | 'Suelta para Detenerte(2)'
    | 'Finalizado'
const state = ref<ESTADOS>('Inicia')
const gameStatus = ref<'NONE' | 'w' | 'l' >('NONE')

async function init() {
  echoSimplePeerHolder = new EchoWebRtc('gamer', userId.value, channelId!, null)
  echoSimplePeerHolder.onStream = function (streams: readonly MediaStream[]) {
    console.log('got stream', streams)
    if (streams[0].active && remoteVideo$.value) {
      remoteVideo$.value.srcObject = streams[0];
    }
  }
  echoSimplePeerHolder.setDataChannelListener(function (event: MessageEvent<string>) {
    let data = event.data
    let obj = JSON.parse(data) as { type: 'SERIAL', letter: 'w' | 'l' }
    console.log('[peer message]', obj.letter)
    gameStatus.value = obj.letter

    // If the game status is 'w' (win), record the win via API
    if (obj.letter === 'w') {
      recordWin();
    }

    messageModal$.value?.showModal()
  });
}
async function restart() {
  // Reset the game state locally
  gameStatus.value = 'NONE'
  state.value = 'Inicia'
  echoSimplePeerHolder.send({type: 'RESET', letter: 'x'})

  try {
    // First leave the current queue position
    await axios.post(route('queue.leave', channelId));

    // Then join the queue again (at the end)
    const response = await axios.post(route('queue.join', channelId));

    if (response.data.success) {
      // If the user is immediately the current player (empty queue), stay on the game page
      if (response.data.should_redirect) {
        // Refresh the page to reset the game state properly
        window.location.reload();
      } else {
        // Otherwise, redirect to the queue page
        router.visit(route('machine.queue', channelId));
      }
    } else {
      // If there was an error joining the queue, show the message
      alert(response.data.message || 'Error al volver a jugar. Por favor, inténtalo de nuevo.');
      // Redirect to the queue page
      router.visit(route('machine.queue', channelId));
    }
  } catch (error) {
    console.error('Failed to restart game:', error);
    alert('Error al volver a jugar. Por favor, inténtalo de nuevo.');
    // Redirect to the queue page as a fallback
    router.visit(route('machine.queue', channelId));
  }
}

async function byeBye() {
  await echoSimplePeerHolder.leaving();
  location.href = '/'
}
// End the game and notify the server
async function endGame() {
  try {
    // Clean up WebRTC connection
    if (echoSimplePeerHolder) {
      await echoSimplePeerHolder.leaving();
    }

    // Notify the server that the game has ended
    await axios.post(route('queue.leave', channelId));

    // Redirect to the queue page
    router.visit(route('machine.queue', channelId));
  } catch (error) {
    console.error('Failed to end game:', error);
  }
}

// Record a win via the API
async function recordWin() {
  try {
    console.log('Recording win for machine:', channelId);
    const response = await axios.post(route('game.win', channelId));

    if (response.data.success) {
      console.log('Win recorded successfully:', response.data);
    } else {
      console.error('Failed to record win:', response.data.message);
    }
  } catch (error) {
    console.error('Error recording win:', error);
  }
}

// Refresh queue data
async function refreshQueueData() {
  try {
    if (userPosition) {
      const positionResponse = await axios.get(route('queue.position', channelId));
      Object.assign(userPosition, positionResponse.data);

      // If user is no longer the current player, redirect to queue page
      if (!positionResponse.data.is_playing) {
        router.visit(route('machine.queue', channelId));
      }
    }
  } catch (error) {
    console.error('Failed to refresh queue data:', error);
  }
}

let refreshInterval: NodeJS.Timeout | null = null;

onMounted(() => {
  if (canPlay) {
    init();

    // Set up auto-refresh for queue data
    refreshInterval = setInterval(refreshQueueData, 5000);
  }
})

onUnmounted(() => {
  // Clean up
  if (echoSimplePeerHolder) {
    echoSimplePeerHolder.leaving();
  }

  // Clear the refresh interval
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
})


let startEventCleanup = router.on('start', async function (event) {
  console.log('nav start', event)
  startEventCleanup()
  await echoSimplePeerHolder.leaving()
})

window.addEventListener('beforeunload', () => echoSimplePeerHolder.leaving())

let keyIsDown: Record<string, boolean> = {}
window.addEventListener('keyup', function (event: KeyboardEvent) {
  if (keyIsDown[event.key]) {
    // echoSimplePeerHolder.send({type: 'UP', letter: event.key})
    mouseUp()
  }
  keyIsDown[event.key] = false//*/
})
window.addEventListener('keydown', function (event: KeyboardEvent) {
  if (!keyIsDown[event.key]) {
    mouseDown()
  }
  keyIsDown[event.key] = true;
})

function mouseDown() {
  if (state.value === 'Inicia') {
    echoSimplePeerHolder.send({type: 'DOWN', letter: 's'})
  } else if (state.value === 'Manten presionado para avanzar(1)' || state.value === 'Manten presionado para avanzar(2)') {
    echoSimplePeerHolder.send({type: 'DOWN', letter: 'p'})
    if (state.value === 'Manten presionado para avanzar(1)') {
      state.value = 'Suelta para Detenerte(1)'
    } else {
      state.value = 'Suelta para Detenerte(2)'
    }
  }
}

function mouseUp() {
  if (state.value === 'Inicia') {
    state.value = 'Manten presionado para avanzar(1)'
  } else if (state.value === 'Suelta para Detenerte(1)' || state.value === 'Suelta para Detenerte(2)') {
    echoSimplePeerHolder.send({type: 'DOWN', letter: 'r'})
    if (state.value === 'Suelta para Detenerte(1)') {
      state.value = 'Manten presionado para avanzar(2)'
    } else {
      state.value = 'Finalizado'
    }
  }
}
</script>

<template>
  <AppLayout title="Juego de Máquina">
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-bold text-2xl text-gray-800 dark:text-gray-200 leading-tight flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Máquina de Premios
        </h2>
        <div v-if="machine && machine.prize" class="flex items-center bg-indigo-900 bg-opacity-50 px-4 py-2 rounded-lg">
          <div class="mr-3">
            <p class="text-xs text-indigo-300">Premio actual:</p>
            <p class="text-sm font-bold text-white">{{ machine.prize.title }}</p>
          </div>
          <div class="h-10 w-10 bg-indigo-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold">{{ machine.prize.price }}</span>
          </div>
        </div>
      </div>
    </template>

    <div class="py-8">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-gradient-to-b from-gray-900 to-gray-800 overflow-hidden shadow-2xl rounded-xl p-6 border border-gray-700">
          <div v-if="!canPlay" class="text-center py-12 max-w-2xl mx-auto">
            <!-- Mensaje de error con icono -->
            <div class="mb-8 flex flex-col items-center">
              <div class="w-20 h-20 rounded-full bg-red-900 bg-opacity-30 flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 class="text-2xl font-bold mb-2 text-red-400">{{ message }}</h3>
              <p class="text-gray-400">No puedes jugar en este momento. Revisa los detalles a continuación.</p>
            </div>

            <!-- Detalles del premio y saldo -->
            <div v-if="machine && machine.prize" class="mb-8 bg-gray-800 bg-opacity-50 p-6 rounded-xl border border-gray-700 shadow-inner">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 bg-gray-900 bg-opacity-50 rounded-lg">
                  <p class="text-sm text-gray-400 mb-1">Premio</p>
                  <p class="text-lg font-bold text-white">{{ machine.prize.title }}</p>
                </div>
                <div class="p-4 bg-gray-900 bg-opacity-50 rounded-lg">
                  <p class="text-sm text-gray-400 mb-1">Costo para jugar</p>
                  <p class="text-lg font-bold text-yellow-400">{{ machine.prize.price }} puntos</p>
                </div>
                <div class="p-4 bg-gray-900 bg-opacity-50 rounded-lg">
                  <p class="text-sm text-gray-400 mb-1">Tu saldo</p>
                  <p class="text-lg font-bold" :class="userBalance < machine.prize.price ? 'text-red-400' : 'text-green-400'">{{ userBalance }} puntos</p>
                </div>
              </div>
            </div>

            <!-- Botones de acción -->
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link
                :href="route('store.index')"
                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Comprar Puntos
              </Link>
              <Link
                :href="route('home')"
                class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Volver al Inicio
              </Link>
            </div>
          </div>

          <div v-else class="flex flex-wrap gap-4">
            <div class="w-full md:w-1/2 lg:w-2/3">
              <div class="flex flex-wrap gap-4">
                <div class="w-full md:w-1/2">
                  <video ref="remotevideo" class="game-video" autoplay muted></video>
                </div>
                <div class="w-full md:w-1/2">
                  <video ref="remotevideo2" class="w-full h-auto" autoplay muted></video>
                </div>
              </div>

              <!-- Game Controls -->
              <div class="mt-8 flex flex-col items-center">
                <!-- Estado del juego -->
                <div class="mb-4 bg-gray-800 rounded-lg px-6 py-3 shadow-inner border border-gray-700">
                  <p class="text-lg font-medium text-center" :class="{
                    'text-blue-400': state === 'Inicia',
                    'text-green-400': state.includes('Manten'),
                    'text-yellow-400': state.includes('Suelta'),
                    'text-purple-400': state === 'Finalizado'
                  }">
                    {{ state }}
                  </p>
                </div>

                <!-- Botón principal de control -->
                <div class="relative">
                  <!-- Efecto de brillo alrededor del botón -->
                  <div class="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-full opacity-70 blur-lg animate-pulse"></div>

                  <!-- Botón principal -->
                  <button
                    class="relative w-48 h-48 rounded-full bg-gradient-to-br from-indigo-600 to-purple-700 hover:from-indigo-500 hover:to-purple-600 shadow-xl border-4 border-indigo-900 focus:outline-none transform transition-all duration-200 active:scale-95 z-10 flex items-center justify-center"
                    v-on:mousedown="mouseDown"
                    v-on:mouseup="mouseUp"
                    v-on:touchstart="mouseDown"
                    v-on:touchend="mouseUp"
                  >
                    <span class="text-white text-xl font-bold">
                      <span v-if="state === 'Inicia'">INICIAR</span>
                      <span v-else-if="state.includes('Manten')">MANTENER</span>
                      <span v-else-if="state.includes('Suelta')">SOLTAR</span>
                      <span v-else>FINALIZADO</span>
                    </span>
                  </button>
                </div>

                <!-- Instrucciones -->
                <div class="mt-6 text-gray-400 text-sm text-center max-w-md">
                  <p>Presiona el botón para iniciar el juego y sigue las instrucciones para controlar la máquina.</p>
                  <p class="mt-2">También puedes usar cualquier tecla del teclado para controlar el juego.</p>
                </div>
              </div>
            </div>

            <div class="w-full md:w-1/2 lg:w-1/3">
              <!-- Player Info -->
              <div class="bg-gradient-to-br from-gray-800 to-gray-900 p-6 rounded-xl mb-6 shadow-lg border border-gray-700">
                <h3 class="text-xl font-bold mb-4 text-white flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Tu Juego
                </h3>
                <div class="mb-6 bg-gray-700 bg-opacity-50 p-4 rounded-lg">
                  <div class="flex justify-between items-center mb-2">
                    <span class="text-gray-300">Tu saldo:</span>
                    <span class="text-xl font-bold text-green-400">{{ userBalance }} puntos</span>
                  </div>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-300">Costo por juego:</span>
                    <span class="text-lg font-medium text-yellow-400">{{ machine.prize.price }} puntos</span>
                  </div>
                </div>
                <button
                  @click="endGame"
                  class="w-full py-3 px-4 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  Terminar Juego
                </button>
              </div>

              <!-- Queue Info -->
              <div v-if="queueInfo" class="bg-gradient-to-br from-gray-800 to-gray-900 p-6 rounded-xl shadow-lg border border-gray-700">
                <h3 class="text-xl font-bold mb-4 text-white flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Estado de la Cola
                </h3>
                <div class="bg-gray-700 bg-opacity-50 p-4 rounded-lg">
                  <div class="flex justify-between items-center mb-3">
                    <span class="text-gray-300">Jugadores esperando:</span>
                    <span class="text-lg font-bold px-3 py-1 bg-blue-900 text-blue-200 rounded-full">{{ queueInfo.total_in_queue }}</span>
                  </div>
                  <div v-if="queueInfo.waiting_players.length > 0" class="mt-3 pt-3 border-t border-gray-600">
                    <p class="text-gray-300 mb-2">Siguiente jugador:</p>
                    <div class="flex items-center bg-gray-800 p-2 rounded-lg">
                      <div class="w-8 h-8 rounded-full bg-indigo-600 flex items-center justify-center mr-3">
                        <span class="text-white font-bold">{{ queueInfo.waiting_players[0].username.charAt(0).toUpperCase() }}</span>
                      </div>
                      <span class="text-white font-medium">{{ queueInfo.waiting_players[0].username }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
      <dialog class="modal" ref="messageModal" closeby="">
        <div class="modal-box bg-gray-800 border border-gray-700 rounded-lg shadow-xl max-w-md mx-auto p-6">
          <!-- Contenido para victoria -->
          <div v-if="gameStatus === 'w'" class="text-center">
            <div class="mb-4 flex justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-yellow-400 mb-2">¡Felicidades!</h3>
            <p class="text-gray-300 mb-6">¡Has ganado el premio! Continúa jugando para ganar más premios.</p>
          </div>

          <!-- Contenido para derrota -->
          <div v-if="gameStatus === 'l'" class="text-center">
            <div class="mb-4 flex justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-blue-400 mb-2">¡Casi lo logras!</h3>
            <p class="text-gray-300 mb-6">No te rindas, ¡sigue intentándolo para ganar increíbles premios!</p>
          </div>

          <!-- Botones de acción -->
          <div class="flex justify-center space-x-4">
            <button
              class="px-5 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
              v-on:click="byeBye"
            >
              Salir
            </button>
            <button
              class="px-5 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors duration-200"
              v-on:click="restart"
            >
              Volver a jugar
            </button>
          </div>
        </div>
      </dialog>
  </AppLayout>
</template>
