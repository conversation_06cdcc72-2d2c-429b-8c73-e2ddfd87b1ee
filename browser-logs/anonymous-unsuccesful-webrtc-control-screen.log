spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PRESENCE CHANNEL - JOINING EVENT
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] User joining: {raw: {…}, type: 'object', keys: Array(3), userId: -1741267134, name: 'Observador Dorado 193', …}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Processing joining user: Observador Dorado 193 (-1741267134) - Anonymous: true
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Controller mode: creating peer connection for new spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] CREATING PEER CONNECTION
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Creating peer for target: -1741267134, Mode: controller
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] RTCPeerConnection created with ICE servers: (2) [{…}, {…}]
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Adding tracks from stream to peer. Stream ID: b1ff7cc7-9ca0-4acb-ae89-dff0c222bae9, Track count: 1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Adding track 1: video (0109d1cd-2819-4b67-b9a5-4d066c359fd2) - enabled: true, readyState: live
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Peer connection setup complete for target: -1741267134
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Starting offer for spectator: -1741267134
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] CREATING WEBRTC OFFER
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Creating offer...
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Peer state before offer: {signalingState: 'stable', iceConnectionState: 'new', connectionState: 'new'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Negotiation needed event triggered
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Offer created, setting local description
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Signaling state: have-local-offer
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Local description set, sending offer
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'offer', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Offer sent successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] ICE gathering state: gathering
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:3039695071 1 udp 2122194687 *********** …eration 0 ufrag CZ5g network-id 1 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:2545795446 1 udp 2122129151 ************…eration 0 ufrag CZ5g network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:4035906095 1 udp 2122265343 fd7a:115c:a1…eration 0 ufrag CZ5g network-id 2 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '[fd7a:115c:a1e0:x:x:x:x:x]', port: 56651, hostCandidate: '[fd7a:115c:a1e0:x:x:x:x:x]:56651', url: 'stun:stun.akiba.barrazo.com:3478', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '[fd7a:115c:a1e0:x:x:x:x:x]', port: 56651, hostCandidate: '[fd7a:115c:a1e0:x:x:x:x:x]:56651', url: 'turn:stun.akiba.barrazo.com:3478?transport=udp', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:3417322962 1 tcp 1518214911 *********** …eration 0 ufrag CZ5g network-id 1 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:3911443579 1 tcp 1518149375 ************…eration 0 ufrag CZ5g network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:2383490850 1 tcp 1518285567 fd7a:115c:a1…eration 0 ufrag CZ5g network-id 2 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:3611800543 1 udp 1685921535 189.218.5.13…eration 0 ufrag CZ5g network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [-1741267134] Sending ICE candidate: {candidate: 'candidate:3594759215 1 udp 41754623 95.216.217.245…eration 0 ufrag CZ5g network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.-1741267134': {type: 'candidate', from: 61, to: -1741267134, dataKeys: Array(2)}
