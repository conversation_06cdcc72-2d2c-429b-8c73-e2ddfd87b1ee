spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PRESENCE CHANNEL - LEAVING EVENT
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] User leaving: {userId: 136, name: 'Administra<PERSON> del Sistema', anon: false, isSelf: false}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Controller mode: cleaning up peer connection for spectator: 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Peer connection closed and removed for: 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PRESENCE CHANNEL - JOINING EVENT
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] User joining: {raw: {…}, type: 'object', keys: Array(3), userId: 136, name: '<PERSON><PERSON><PERSON><PERSON> del Sistema', …}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Processing joining user: Administrador del Sistema (136) - Anonymous: false
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Controller mode: creating peer connection for new spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] CREATING PEER CONNECTION
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Creating peer for target: 136, Mode: controller
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] RTCPeerConnection created with ICE servers: (2) [{…}, {…}]
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Adding tracks from stream to peer. Stream ID: b1ff7cc7-9ca0-4acb-ae89-dff0c222bae9, Track count: 1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Adding track 1: video (0109d1cd-2819-4b67-b9a5-4d066c359fd2) - enabled: true, readyState: live
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Peer connection setup complete for target: 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Starting offer for spectator: 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] CREATING WEBRTC OFFER
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Creating offer...
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Peer state before offer: {signalingState: 'stable', iceConnectionState: 'new', connectionState: 'new'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Negotiation needed event triggered
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Offer created, setting local description
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Signaling state: have-local-offer
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Local description set, sending offer
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'offer', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Offer sent successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE gathering state: gathering
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:145684830 1 udp 2122194687 *********** 6…eration 0 ufrag ljtl network-id 1 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:2810606019 1 udp 2122129151 ************…eration 0 ufrag ljtl network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:3042355800 1 udp 2122265343 fd7a:115c:a1…eration 0 ufrag ljtl network-id 2 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '[fd7a:115c:a1e0:x:x:x:x:x]', port: 60884, hostCandidate: '[fd7a:115c:a1e0:x:x:x:x:x]:60884', url: 'stun:stun.akiba.barrazo.com:3478', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '[fd7a:115c:a1e0:x:x:x:x:x]', port: 60884, hostCandidate: '[fd7a:115c:a1e0:x:x:x:x:x]:60884', url: 'turn:stun.akiba.barrazo.com:3478?transport=udp', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:1986075590 1 tcp 1518214911 *********** …eration 0 ufrag ljtl network-id 1 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:3645477723 1 tcp 1518149375 ************…eration 0 ufrag ljtl network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:3415820480 1 tcp 1518285567 fd7a:115c:a1…eration 0 ufrag ljtl network-id 2 network-cost 50', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:147624189 1 udp 1685921535 189.218.5.13 …eration 0 ufrag ljtl network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Received controller signal: {type: 'answer', from: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Processing answer from 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Peer state before answer: {signalingState: 'have-local-offer', iceConnectionState: 'new', connectionState: 'new'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Received controller signal: {type: 'candidate', from: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Processing ICE candidate from 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Cannot add ICE candidate - no remote description set yet
unifiedWhisperListener @ spectator-web-rtc-aHnBMtwR.js:1
listenControllerMachineSignalWhisper @ spectator-web-rtc-aHnBMtwR.js:1
emit @ app-daHjiuxr.js:13
handleEvent @ app-daHjiuxr.js:13
(anonymous) @ app-daHjiuxr.js:14
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
onMessage @ app-daHjiuxr.js:13
socket.onmessage @ app-daHjiuxr.js:13
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Signaling state: stable
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Answer processed successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Sending ICE candidate: {candidate: 'candidate:3044042494 1 udp 41754623 **************…eration 0 ufrag ljtl network-id 3 network-cost 10', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Sending signal via whisper to 'signal.136': {type: 'candidate', from: 61, to: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Received controller signal: {type: 'candidate', from: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Processing ICE candidate from 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE connection state: checking
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Connection state: connecting
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Received controller signal: {type: 'candidate', from: 136, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] Processing ICE candidate from 136
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE gathering state: complete
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE gathering complete (null candidate)
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] ICE connection state: connected
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:controller:61] [136] Connection state: connected
 [SpectatorWebRTC:controller:61] [136] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '100.66.0.x', port: 60882, hostCandidate: '100.66.0.x:60882', url: 'stun:stun.akiba.barrazo.com:3478', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
 [SpectatorWebRTC:controller:61] [136] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '100.66.0.x', port: 60882, hostCandidate: '100.66.0.x:60882', url: 'turn:stun.akiba.barrazo.com:3478?transport=udp', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
