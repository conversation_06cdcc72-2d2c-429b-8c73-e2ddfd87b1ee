UserService-DJu10fKH.js:1 User set: {userId: 'anon_h17wxo36', name: 'Espectador Anónimo', anon: true}
MachineSpectator-TEdxnULN.js:1 Initializing WebRTC in MachineSpectator
MachineSpectator-TEdxnULN.js:1 User ID: anon_h17wxo36
MachineSpectator-TEdxnULN.js:1 User Data: {userId: 'anon_h17wxo36', name: 'Espectador Anónimo', anon: true}
MachineSpectator-TEdxnULN.js:1 Is Anonymous: true
MachineSpectator-TEdxnULN.js:1 Channel ID: 826b34ee-372c-4074-9cd5-48005ec3ba5d
MachineSpectator-TEdxnULN.js:1 Video element exists: true
MachineSpectator-TEdxnULN.js:1 Video element configured
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] INITIALIZATION
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Constructor called with: {mode: 'spectator', userId: 'anon_h17wxo36', machineId: '826b34ee-372c-4074-9cd5-48005ec3ba5d', hasStream: false, streamTracks: 0, …}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Joining presence channel: machineSpectator.826b34ee-372c-4074-9cd5-48005ec3ba5d
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Setting up presence channel event handlers...
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Setting up whisper listeners for mode: spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Spectator mode: listening for 'signal.anon_h17wxo36' whispers
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Initialization complete
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] PRESENCE CHANNEL - HERE EVENT
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Raw users data: (2) [{…}, {…}]
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Users type: object, Array: true, Length: 2
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Processing 2 users in channel
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] User 1: {userId: 61, name: 'Florida Wilkinson', anon: false, isSelf: false, keys: Array(3)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] User 2: {userId: -1741267134, name: 'Observador Dorado 193', anon: true, isSelf: false, keys: Array(3)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Added spectator: Florida Wilkinson (61) - Anonymous: false
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Added spectator: Observador Dorado 193 (-1741267134) - Anonymous: true
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Total spectators (excluding self): 2
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Current mode: spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Spectator mode: creating peer connection to receive stream
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] CREATING PEER CONNECTION
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Creating peer for target: null, Mode: spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] RTCPeerConnection created with ICE servers: (2) [{…}, {…}]
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] No stream available to add tracks from
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:anon_h17wxo36] Peer connection setup complete for target: null
