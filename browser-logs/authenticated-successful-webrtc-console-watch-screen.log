UserService-DJu10fKH.js:1 User set: {userId: 136, name: '<PERSON><PERSON>stra<PERSON> del Sistema', anon: false}
MachineSpectator-TEdxnULN.js:1 Initializing WebRTC in MachineSpectator
MachineSpectator-TEdxnULN.js:1 User ID: 136
MachineSpectator-TEdxnULN.js:1 User Data: {userId: 136, name: 'Administrador del Sistema', anon: false}
MachineSpectator-TEdxnULN.js:1 Is Anonymous: false
MachineSpectator-TEdxnULN.js:1 Channel ID: 826b34ee-372c-4074-9cd5-48005ec3ba5d
MachineSpectator-TEdxnULN.js:1 Video element exists: true
MachineSpectator-TEdxnULN.js:1 Video element configured
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] INITIALIZATION
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Constructor called with: {mode: 'spectator', userId: 136, machineId: '826b34ee-372c-4074-9cd5-48005ec3ba5d', hasStream: false, streamTracks: 0, …}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Joining presence channel: machineSpectator.826b34ee-372c-4074-9cd5-48005ec3ba5d
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Setting up presence channel event handlers...
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Setting up whisper listeners for mode: spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Spectator mode: listening for 'signal.136' whispers
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Initialization complete
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PRESENCE CHANNEL - HERE EVENT
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Raw users data: (2) [{…}, {…}]
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Users type: object, Array: true, Length: 2
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing 2 users in channel
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] User 1: {userId: 61, name: 'Florida Wilkinson', anon: false, isSelf: false, keys: Array(3)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] User 2: {userId: 136, name: 'Administrador del Sistema', anon: false, isSelf: true, keys: Array(3)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Added spectator: Florida Wilkinson (61) - Anonymous: false
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Skipping self (136)
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Total spectators (excluding self): 1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Current mode: spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Spectator mode: creating peer connection to receive stream
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] CREATING PEER CONNECTION
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Creating peer for target: null, Mode: spectator
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] RTCPeerConnection created with ICE servers: (2) [{…}, {…}]
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] No stream available to add tracks from
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Peer connection setup complete for target: null
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'offer', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing offer from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Peer state before offer: {signalingState: 'stable', iceConnectionState: 'new', connectionState: 'new'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Cannot add ICE candidate - no remote description set yet
unifiedWhisperListener @ spectator-web-rtc-aHnBMtwR.js:1
listenControllerSpectatorSignalWhisper @ spectator-web-rtc-aHnBMtwR.js:1
emit @ app-daHjiuxr.js:13
handleEvent @ app-daHjiuxr.js:13
(anonymous) @ app-daHjiuxr.js:14
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
onMessage @ app-daHjiuxr.js:13
socket.onmessage @ app-daHjiuxr.js:13
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Cannot add ICE candidate - no remote description set yet
unifiedWhisperListener @ spectator-web-rtc-aHnBMtwR.js:1
listenControllerSpectatorSignalWhisper @ spectator-web-rtc-aHnBMtwR.js:1
emit @ app-daHjiuxr.js:13
handleEvent @ app-daHjiuxr.js:13
(anonymous) @ app-daHjiuxr.js:14
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
onMessage @ app-daHjiuxr.js:13
socket.onmessage @ app-daHjiuxr.js:13
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Cannot add ICE candidate - no remote description set yet
unifiedWhisperListener @ spectator-web-rtc-aHnBMtwR.js:1
listenControllerSpectatorSignalWhisper @ spectator-web-rtc-aHnBMtwR.js:1
emit @ app-daHjiuxr.js:13
handleEvent @ app-daHjiuxr.js:13
(anonymous) @ app-daHjiuxr.js:14
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
message @ app-daHjiuxr.js:13
emit @ app-daHjiuxr.js:13
onMessage @ app-daHjiuxr.js:13
socket.onmessage @ app-daHjiuxr.js:13
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Signaling state: have-remote-offer
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] RECEIVED TRACK EVENT
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Track event received: {track: {…}, streamCount: 1, receiver: 'present', transceiver: 'present'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Stream details: {id: 'b1ff7cc7-9ca0-4acb-ae89-dff0c222bae9', active: true, tracks: Array(1)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Setting video element srcObject
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Remote description set, creating answer
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Video load started
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Signaling state: stable
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Answer created and local description set
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Sending signal via whisper to 'signal': {type: 'answer', from: 136, to: null, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE gathering state: gathering
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Sending ICE candidate: {candidate: 'candidate:3951042262 1 udp 2113937151 721906bd-76f…typ host generation 0 ufrag U0Fd network-cost 999', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Sending signal via whisper to 'signal': {type: 'candidate', from: 136, to: null, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '[0:0:0:x:x:x:x:x]', port: 59345, hostCandidate: '[0:0:0:x:x:x:x:x]:59345', url: 'stun:stun.akiba.barrazo.com:3478', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE candidate error: RTCPeerConnectionIceErrorEvent {isTrusted: true, address: '[0:0:0:x:x:x:x:x]', port: 59345, hostCandidate: '[0:0:0:x:x:x:x:x]:59345', url: 'turn:stun.akiba.barrazo.com:3478?transport=udp', …}
t.onicecandidateerror @ spectator-web-rtc-aHnBMtwR.js:1
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Sending ICE candidate: {candidate: 'candidate:3136322574 1 udp 1677729535 189.218.5.13… rport 0 generation 0 ufrag U0Fd network-cost 999', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Sending signal via whisper to 'signal': {type: 'candidate', from: 136, to: null, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE connection state: checking
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Connection state: connecting
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Received spectator signal: {type: 'candidate', from: 61, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] PROCESSING WEBRTC SIGNAL
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Processing ICE candidate from 61
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] ICE candidate added successfully
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Sending ICE candidate: {candidate: 'candidate:625061459 1 udp 33562623 95.216.217.245 …rt 50362 generation 0 ufrag U0Fd network-cost 999', sdpMLineIndex: 0, sdpMid: '0'}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Sending signal via whisper to 'signal': {type: 'candidate', from: 136, to: null, dataKeys: Array(2)}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE gathering state: complete
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE gathering complete (null candidate)
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] ICE connection state: connected
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] [null] Connection state: connected
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Video metadata loaded: {dimensions: '360x512', duration: Infinity, readyState: 4}
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Video can start playing
spectator-web-rtc-aHnBMtwR.js:1 [SpectatorWebRTC:spectator:136] Video started playing
