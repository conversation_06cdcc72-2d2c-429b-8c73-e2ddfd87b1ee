<?php

use App\Services\AnonymousUserService;
use Illuminate\Support\Facades\Broadcast;

Broadcast::routes([
    'middleware' => ['web',  \App\Http\Controllers\AuthenticateBroadcastGuests::class],
]);

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int)$user->id === (int)$id;
});


Broadcast::channel('machineControl.{clawUserId}', function (Illuminate\Foundation\Auth\User $user, $clawUserId) {
    return ['userId' => $user->id, 'name' => $user->name];
});

Broadcast::channel('machineSpectator.{clawUserId}', function (Illuminate\Foundation\Auth\User $user, $clawUserId) {
    return ['userId' => $user->id, 'name' => $user->name];
});


Broadcast::channel('publicity', function ($user, $clawUserId) {
    // Public channel - always allow
    return true;
});
