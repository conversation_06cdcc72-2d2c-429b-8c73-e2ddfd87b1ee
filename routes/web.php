<?php

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Override default Laravel authentication routes with custom views
Route::get('/login', [\App\Http\Controllers\CustomAuthController::class, 'showLoginForm'])->name('login');
Route::get('/register', [\App\Http\Controllers\CustomAuthController::class, 'showRegistrationForm'])->name('register');

// Social Authentication Routes
Route::get('/auth/{provider}', [\App\Http\Controllers\SocialiteController::class, 'redirectToProvider'])->name('social.redirect');
Route::get('/auth/{provider}/callback', [\App\Http\Controllers\SocialiteController::class, 'handleProviderCallback'])->name('social.callback');

// Test page for anonymous users with Echo
Route::get('/test-anonymous-echo', function () {
    return Inertia::render('TestAnonymousEcho');
})->middleware(['web'])->name('test.anonymous.echo');

// Main Routes
Route::get('/', [\App\Http\Controllers\MainController::class, 'home'])->name('home');
Route::get('/category/{category:slug}', [\App\Http\Controllers\MainController::class, 'home'])->name('category');
Route::get('/game/{machineId}/watch', [\App\Http\Controllers\MainController::class, 'watchGame'])
    ->name('machine.watch');
Route::get('/game/{machineId}/play', [\App\Http\Controllers\MainController::class, 'playGame'])->name('machine.play');
Route::get('/game/{machineId}/control', [\App\Http\Controllers\MainController::class, 'controlGame'])
    ->middleware('machine.auth')
    ->name('machine.control');
Route::get('/game/{machineId}/queue', [\App\Http\Controllers\QueueController::class, 'showQueuePage'])->name('machine.queue');

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified',])->group(function () {
    Route::get('/dashboard', function () {
        return Inertia::render('Dashboard');
    })->name('dashboard');
});

Route::prefix('game')->name('game.')->group(function () {
    Route::get('machine-control', [\App\Http\Controllers\GameController::class, 'machineController'])->name('machine-controller');
    Route::get('machine-game', [\App\Http\Controllers\GameController::class, 'machineGame'])->name('machine-game');
    Route::get('machine-spectator', [\App\Http\Controllers\GameController::class, 'machineSpectator'])->name('machine-spectator');
});

Route::prefix('store')->name('store.')->group(function () {
    Route::get('', [\App\Http\Controllers\StoreController::class, 'index'])->name('index');
});

Route::prefix('mp')->name('mp.')->middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->group(function () {
    Route::post('preference/{pointPackage}',[\App\Http\Controllers\api\MercadoPagoController::class,'generatePaymentPreference'])->name('preference');
    Route::post('process-card-payment', [\App\Http\Controllers\api\MercadoPagoController::class,'cardPayment'])->name('process-card-payment');
});

Route::prefix('user/profile')->name('profile.')->group(function () {
    Route::get('',[\App\Http\Controllers\ProfileController::class,'show'])->name('show');
    Route::get('wallet',[\App\Http\Controllers\ProfileController::class,'wallet'])->name('wallet');
});

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->prefix('prizes')->name('prizes.')->group(function () {
    Route::prefix('bundles')->name('bundles.')->group(function () {
        Route::get('', [\App\Http\Controllers\PrizeBundleController::class, 'index'])->name('index');
        Route::get('create', [\App\Http\Controllers\PrizeBundleController::class, 'create'])->name('create');
        Route::post('', [\App\Http\Controllers\PrizeBundleController::class, 'store'])->name('store');
        Route::post('store-with-delivery', [\App\Http\Controllers\PrizeBundleController::class, 'storeWithDelivery'])->name('store-with-delivery');
        Route::get('{bundle}', [\App\Http\Controllers\PrizeBundleController::class, 'show'])->name('show');
    });
});

// Queue API routes
Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->prefix('api/queue')->name('queue.')->group(function () {
    Route::post('{machineId}/join', [\App\Http\Controllers\QueueController::class, 'joinQueue'])->name('join');
    Route::post('{machineId}/leave', [\App\Http\Controllers\QueueController::class, 'leaveQueue'])->name('leave');
    Route::get('{machineId}/position', [\App\Http\Controllers\QueueController::class, 'getQueuePosition'])->name('position');
    Route::get('{machineId}/info', [\App\Http\Controllers\QueueController::class, 'getQueueInfo'])->name('info');
});

// Game API routes
Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->prefix('api/game')->name('game.')->group(function () {
    Route::post('{machineId}/win', [\App\Http\Controllers\GameController::class, 'recordWin'])->name('win');
});
